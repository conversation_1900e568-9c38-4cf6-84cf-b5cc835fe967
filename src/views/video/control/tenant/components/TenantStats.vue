<template>
  <div class="tenant-stats">
    <el-row :gutter="20">
      <!-- 租户权限分布柱状图 -->
      <el-col :span="12">
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">租户权限分布</span>
              <div class="time-filter">
                <el-radio-group v-model="timeFilter" size="mini">
                  <el-radio-button label="本周">本周</el-radio-button>
                  <el-radio-button label="本月">本月</el-radio-button>
                  <el-radio-button label="全部">全部</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div ref="permissionChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 租户类型占比饼图 -->
      <el-col :span="12">
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">租户类型占比</span>
            </div>
          </template>
          <div ref="typeChart" class="chart-container"></div>
          <div class="legend-container absolute">
            <div v-for="item in typeData" :key="item.name" class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
              <span class="legend-label">{{ item.name }}</span>
              <span class="legend-value">{{ item.value }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TenantStats',
  data() {
    return {
      timeFilter: '本月',
      permissionChart: null,
      typeChart: null,
      // 权限分布数据
      permissionData: [
        { name: '科技创新有限公司', basic: 1200, advanced: 800, special: 200 },
        { name: '华夏教育', basic: 2000, advanced: 1200, special: 300 },
        { name: '科技大学', basic: 600, advanced: 400, special: 100 },
        { name: '集团', basic: 100, advanced: 50, special: 20 },
        { name: '未来科技发展有限公司', basic: 300, advanced: 200, special: 80 },
        { name: '智慧城市运营中心', basic: 800, advanced: 400, special: 150 }
      ],
      // 类型占比数据
      typeData: [
        { name: '企业租户', value: 45.2, color: '#409EFF' },
        { name: '教育机构', value: 28.6, color: '#67C23A' },
        { name: '政府机构', value: 18.7, color: '#E6A23C' },
        { name: '个人租户', value: 7.5, color: '#F56C6C' }
      ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.permissionChart) {
      this.permissionChart.dispose()
    }
    if (this.typeChart) {
      this.typeChart.dispose()
    }
  },
  watch: {
    timeFilter() {
      this.updatePermissionChart()
    }
  },
  methods: {
    initCharts() {
      this.initPermissionChart()
      this.initTypeChart()
    },

    initPermissionChart() {
      this.permissionChart = echarts.init(this.$refs.permissionChart)
      this.updatePermissionChart()
    },

    updatePermissionChart() {
      const categories = this.permissionData.map(item => item.name)
      const basicData = this.permissionData.map(item => item.basic)
      const advancedData = this.permissionData.map(item => item.advanced)
      const specialData = this.permissionData.map(item => item.special)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['基础权限', '高级权限', '特殊权限'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: {
            rotate: 45,
            interval: 0,
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          name: '数量'
        },
        series: [
          {
            name: '基础权限',
            type: 'bar',
            stack: 'total',
            data: basicData,
            itemStyle: {
              color: '#409EFF',
              borderRadius: [0, 0, 4, 4]
            },
            barMaxWidth: 60
          },
          {
            name: '高级权限',
            type: 'bar',
            stack: 'total',
            data: advancedData,
            itemStyle: {
              color: '#67C23A'
            },
            barMaxWidth: 60
          },
          {
            name: '特殊权限',
            type: 'bar',
            stack: 'total',
            data: specialData,
            itemStyle: {
              color: '#E6A23C',
              borderRadius: [4, 4, 0, 0]
            },
            barMaxWidth: 60
          }
        ]
      }

      this.permissionChart.setOption(option)
    },

    initTypeChart() {
      this.typeChart = echarts.init(this.$refs.typeChart)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}% ({d}%)',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff'
          }
        },
        series: [
          {
            name: '租户类型',
            type: 'pie',
            radius: ['45%', '75%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            labelLine: {
              show: false
            },
            data: this.typeData.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: item.color,
                borderWidth: 2,
                borderColor: '#fff'
              }
            }))
          }
        ]
      }

      this.typeChart.setOption(option)
    },

    handleResize() {
      if (this.permissionChart) {
        this.permissionChart.resize()
      }
      if (this.typeChart) {
        this.typeChart.resize()
      }
    }
  }
}
</script>

<style scoped>
.tenant-stats {
}

.stats-card {
  height: 400px;
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.time-filter {
  margin-left: auto;
}

.chart-container {
  height: 280px;
  width: 100%;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 0px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-label {
  font-size: 14px;
  color: #606266;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    height: 240px;
  }

  .legend-container {
    gap: 15px;
  }

  .legend-item {
    gap: 6px;
  }

  .legend-label,
  .legend-value {
    font-size: 12px;
  }
}
</style>
