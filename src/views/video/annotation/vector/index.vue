<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <!-- 无额外工具栏按钮 -->
    </template>

    <!-- 索引状态列自定义渲染 -->
    <template #table:value7:simple="{ row }">
      <el-tag :type="getIndexStatusTagType(row.value7)">
        {{ row.value7 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleView(row)">
        <i class="el-icon-view"></i>
        查看
      </el-button>
      <el-button type="text" size="mini" @click="handleRefresh(row)">
        <i class="el-icon-refresh"></i>
        刷新
      </el-button>
      <el-button type="text" size="mini" @click="handleDownload(row)">
        <i class="el-icon-download"></i>
        下载
      </el-button>
      <el-button type="text" size="mini" @click="handleDelete(row)">
        <i class="el-icon-delete"></i>
        删除
      </el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { indexStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoVectorManagement',
  data() {
    return {
      tableType: 'video_annotation_vector'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频向量数据',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 存储ID
          value1: {
            type: 'text',
            label: '存储ID',
            align: 'left',
            width: 150,
            search: {
              placeholder: '请输入存储ID'
            }
          },
          // 关联视频任务
          value2: {
            type: 'text',
            label: '关联视频任务',
            align: 'left',
            width: 180,
            search: {
              placeholder: '请输入任务ID'
            }
          },
          // 帧向量总量
          value3: {
            type: 'text',
            label: '帧向量总量',
            align: 'right',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 单帧向量维度
          value4: {
            type: 'text',
            label: '单帧向量维度',
            align: 'right',
            width: 130,
            search: {
              hidden: true
            }
          },
          // 视频识别结果
          value5: {
            type: 'text',
            label: '视频识别结果',
            align: 'left',
            width: 200,
            search: {
              hidden: true
            }
          },
          // 索引状态
          value6: {
            type: 'select',
            label: '索引状态',
            width: 100,
            search: {
              type: 'select',
              options: [
                { label: '全部状态', value: '' },
                ...indexStatus
              ]
            },
            options: indexStatus
          },
          // 存储容量
          value7: {
            type: 'text',
            label: '存储容量',
            align: 'right',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 最近更新时间
          value8: {
            type: 'datetime',
            label: '最近更新时间',
            width: 160,
            search: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    // 获取索引状态标签类型
    getIndexStatusTagType(status) {
      const statusMap = {
        '正常': 'success',
        '需优化': 'warning'
      }
      return statusMap[status] || 'info'
    },

    // 查看操作
    handleView(row) {
      this.$modal.msgInfo(`查看向量数据详情：存储ID ${row.value2}`)
      // 这里可以打开详情弹窗或跳转到详情页面
      console.log('查看向量数据:', row)
    },

    // 刷新操作
    handleRefresh(row) {
      this.$modal.confirm(`确认要刷新存储ID为"${row.value2}"的向量数据吗？`).then(() => {
        // 这里可以调用刷新接口
        console.log('刷新向量数据:', row)
        this.$modal.msgSuccess('刷新成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 下载操作
    handleDownload(row) {
      this.$modal.confirm(`确认要下载存储ID为"${row.value2}"的向量数据吗？`).then(() => {
        // 这里可以调用下载接口
        console.log('下载向量数据:', row)
        this.$modal.msgSuccess('下载任务已启动')
      }).catch(() => {})
    },

    // 删除操作
    handleDelete(row) {
      this.$modal.confirm(`确认要删除存储ID为"${row.value2}"的向量数据吗？此操作不可恢复！`).then(() => {
        // 这里可以调用删除接口
        console.log('删除向量数据:', row)
        this.$modal.msgSuccess('删除成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
